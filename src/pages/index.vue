<template>
  <div>
    <!-- Header Navigation -->
    <v-app-bar
      color="white"
      elevation="1"
      flat
      class="px-4"
    >
      <v-img
        src="@/assets/logo.png"
        alt="Flawless Aesthetics Logo"
        max-width="60"
        max-height="60"
      />

      <v-spacer />

      <!-- Desktop Navigation -->
      <div class="hidden-sm-and-down">
        <v-btn
          class="mx-2"
          color="primary"
          variant="text"
        >
          Home
        </v-btn>
        <v-btn
          class="mx-2"
          color="primary"
          variant="text"
        >
          Services
        </v-btn>
        <v-btn
          class="mx-2"
          color="primary"
          variant="text"
        >
          About
        </v-btn>
        <v-btn
          class="mx-2"
          color="primary"
          variant="text"
        >
          Contact
        </v-btn>
      </div>

      <v-app-bar-nav-icon
        class="hidden-md-and-up"
        @click="drawer = !drawer"
      />
    </v-app-bar>

    <!-- Hero Section -->
    <div class="hero-section" :style="videoError ? `background-image: url(${heroFallback})` : ''">
      <video
        v-if="!videoError"
        ref="videoRef"
        class="hero-video"
        autoplay
        muted
        loop
        playsinline
        preload="metadata"
        :poster="heroFallback"
      >
        <source :src="heroVideo" type="video/mp4">
        Your browser does not support the video tag.
      </video>
      <div class="hero-overlay">
        <v-container>
          <v-row align="center" justify="center" class="text-center">
            <v-col cols="12" md="8">
              <h1 class="hero-title text-white mb-4">
                Revitalize Your Beauty at Flawless Aesthetics and Day Spa
              </h1>
              <p class="hero-subtitle text-white mb-6">
                Experience rejuvenating treatments like chemical peels, microneedling, and massages at our day spa.
              </p>
              <v-btn
                class="mb-4"
                color="white"
                href="tel:7279067397"
                size="large"
                variant="outlined"
              >
                (*************
              </v-btn>
              <p class="text-white">
                1016 Ponce de Leon Blvd, Suite 1, Belleair, FL 33756
              </p>
            </v-col>
          </v-row>
        </v-container>
      </div>
    </div>

    <!-- About Section -->
    <v-container class="py-16">
      <v-row align="center">
        <v-col cols="12" md="6">
          <h2 class="section-title mb-4">Welcome to Flawless Aesthetics Spa</h2>
          <p class="section-text">
            Experience rejuvenation and relaxation with our expert services, including chemical peels, microneedling, cryotherapy, and massage, tailored to enhance your natural beauty and well-being.
          </p>
        </v-col>
        <v-col cols="12" md="6">
          <v-img
            src="@/assets/photo-1600334089648-b0d9d3028eb2.avif"
            alt="Spa Treatment"
            aspect-ratio="1.2"
            class="rounded-lg"
          />
        </v-col>
      </v-row>
    </v-container>

    <!-- Services Section -->
    <v-container fluid class="services-section py-16">
      <v-container>
        <v-row>
          <v-col cols="12" class="text-center mb-8">
            <h2 class="section-title mb-4">Spa Services Offered</h2>
            <p class="section-text">
              Explore our rejuvenating treatments including chemical peels, microneedling, and relaxing massages.
            </p>
          </v-col>
        </v-row>

        <v-row align="center">
          <!-- Large Service Image -->
          <v-col class="mb-6" cols="12" md="6">
            <v-img
              alt="Spa Services"
              aspect-ratio="1.2"
              class="rounded-lg"
              src="@/assets/flawless-microneedling-YlevMng0epC41ZwM.avif"
            />
          </v-col>

          <!-- Services List -->
          <v-col class="pl-md-8" cols="12" md="6">
            <div class="services-list">
              <!-- Soothing Massage Therapy -->
              <div class="service-item mb-8">
                <h3 class="service-item-title mb-3">Soothing Massage Therapy</h3>
                <p class="service-item-text">
                  Relax and unwind with our therapeutic massage services designed to relieve tension.
                </p>
              </div>

              <!-- Revitalizing Chemical Peels -->
              <div class="service-item mb-8">
                <h3 class="service-item-title mb-3">Revitalizing Chemical Peels</h3>
                <p class="service-item-text">
                  Transform your skin with our effective chemical peel treatments tailored to your needs.
                </p>
              </div>

              <!-- Advanced Microneedling -->
              <div class="service-item mb-8">
                <h3 class="service-item-title mb-3">Advanced Microneedling</h3>
                <p class="service-item-text">
                  Achieve smoother skin and reduce scars with our professional microneedling services.
                </p>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </v-container>

    <!-- Testimonial Section -->
    <v-container class="py-16">
      <v-row justify="center">
        <v-col class="text-center" cols="12" md="8">
          <v-card class="testimonial-card pa-8" elevation="0">
            <v-rating
              v-model="rating"
              color="amber"
              density="compact"
              readonly
              size="small"
            />
            <p class="testimonial-text mt-4 mb-4">
              "Flawless Aesthetics transformed my skin with their chemical peels and microneedling. Highly recommend their services!"
            </p>
            <div class="d-flex align-center justify-center">
              <v-avatar class="me-3" size="48">
                <v-img
                  alt="Sarah J."
                  src="@/assets/photo-1506126613408-eca07ce68773.avif"
                />
              </v-avatar>
              <div>
                <p class="testimonial-author mb-0">Sarah J.</p>
              </div>
            </div>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <!-- Gallery Section -->
    <v-container class="gallery-section py-16" fluid>
      <v-container>
        <v-row>
          <v-col class="text-center mb-8" cols="12">
            <h2 class="section-title mb-4">Spa Gallery</h2>
            <p class="section-text">
              Explore our rejuvenating treatments: chemical peels, microneedling,<br>
              and more.
            </p>
          </v-col>
        </v-row>

        <v-row justify="center">
          <v-col
            v-for="(image, index) in galleryImages"
            :key="index"
            class="mb-4"
            cols="6"
            md="3"
          >
            <div
              :ref="el => setGalleryRef(el, index)"
              class="gallery-image-container"
              :class="{ 'animate-fade-up': galleryAnimated[index] }"
              :style="{ animationDelay: `${index * 150}ms` }"
            >
              <v-img
                :alt="image.alt"
                aspect-ratio="1.2"
                class="gallery-image"
                :src="image.src"
              />
            </div>
          </v-col>
        </v-row>
      </v-container>
    </v-container>

    <!-- Footer -->
    <v-footer class="footer-section pa-8">
      <v-container>
        <v-row>
          <v-col class="mb-6" cols="12" md="4">
            <h3 class="footer-title mb-4">Relaxation</h3>
            <p class="footer-text">
              Experience rejuvenating treatments for your well-being.
            </p>
            <div class="mt-4">
              <p class="footer-text"><strong>Beauty</strong></p>
              <p class="footer-text"><strong>Wellness</strong></p>
            </div>
          </v-col>

          <v-col class="mb-6" cols="12" md="4">
            <h3 class="footer-title mb-4">Contact</h3>
            <p class="footer-text mb-2">
              <v-icon class="me-2" size="small">mdi-email</v-icon>
              <EMAIL>
            </p>
            <p class="footer-text mb-2">
              <v-icon class="me-2" size="small">mdi-phone</v-icon>
              (*************
            </p>
            <p class="footer-text">
              <v-icon class="me-2" size="small">mdi-map-marker</v-icon>
              1016 Ponce de Leon Blvd, Suite 1, Belleair, FL 33756
            </p>
          </v-col>

          <v-col class="mb-6" cols="12" md="4">
            <h3 class="footer-title mb-4">Follow Us</h3>
            <div class="d-flex">
              <v-btn
                class="me-2"
                color="primary"
                href="https://www.facebook.com/profile.php?id=100071388243671"
                icon
                size="small"
                target="_blank"
                variant="outlined"
              >
                <v-icon>mdi-facebook</v-icon>
              </v-btn>
              <v-btn
                color="primary"
                href="https://www.instagram.com/flawless_aesthetics_belleair/"
                icon
                size="small"
                target="_blank"
                variant="outlined"
              >
                <v-icon>mdi-instagram</v-icon>
              </v-btn>
            </div>
          </v-col>
        </v-row>

        <v-divider class="my-6" />

        <v-row>
          <v-col class="text-center" cols="12">
            <p class="footer-text">
              © 2024. All rights reserved.
            </p>
          </v-col>
        </v-row>
      </v-container>
    </v-footer>

    <!-- Navigation Drawer for Mobile -->
    <v-navigation-drawer
      v-model="drawer"
      location="right"
      temporary
    >
      <v-list>
        <v-list-item title="Home" />
        <v-list-item title="Services" />
        <v-list-item title="About" />
        <v-list-item title="Contact" />
        <v-divider class="my-2" />
        <v-list-item
          href="tel:7279067397"
          prepend-icon="mdi-phone"
          subtitle="(*************"
          title="Call Now"
        />
        <v-list-item
          prepend-icon="mdi-map-marker"
          subtitle="1016 Ponce de Leon Blvd, Suite 1"
          title="Visit Us"
        />
      </v-list>
    </v-navigation-drawer>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue'
  import heroVideo from '@/assets/5669040-hd_1280_720_30fps.mp4'
  import heroFallback from '@/assets/hero-background.jpg'

  // Import gallery images
  import galleryImage1 from '@/assets/untitled-design-6-YrDla36bxxFZD9vD.avif'
  import galleryImage2 from '@/assets/untitled-design-5-A0xwN3W9ygi6jVVg.avif'
  import galleryImage3 from '@/assets/spa-facial-mP4npZeZg9sxpZv0.avif'
  import galleryImage4 from '@/assets/man-massage-dJo6Jl6D09uGpM3a.avif'

  const drawer = ref(false)
  const videoError = ref(false)
  const videoRef = ref(null)
  const rating = ref(5)

  // Gallery data and animation state
  const galleryImages = [
    {
      src: '@/assets/untitled-design-6-YrDla36bxxFZD9vD.avif',
      alt: 'Spa Treatment Room',
    },
    {
      src: '@/assets/untitled-design-5-A0xwN3W9ygi6jVVg.avif',
      alt: 'Facial Treatment',
    },
    {
      src: '@/assets/spa-facial-mP4npZeZg9sxpZv0.avif',
      alt: 'Spa Facial Treatment',
    },
    {
      src: '@/assets/man-massage-dJo6Jl6D09uGpM3a.avif',
      alt: 'Massage Therapy',
    },
  ]

  const galleryRefs = ref([])
  const galleryAnimated = reactive(Array.from({ length: galleryImages.length }, () => false))

  const setGalleryRef = (el, index) => {
    if (el) {
      galleryRefs.value[index] = el
    }
  }

  onMounted(() => {
    // Video setup
    if (videoRef.value) {
      videoRef.value.addEventListener('error', () => {
        console.error('Video failed to load')
        videoError.value = true
      })

      videoRef.value.addEventListener('loadeddata', () => {
        console.log('Video loaded successfully')
        // Ensure video plays
        videoRef.value.play().catch(error => {
          console.warn('Video autoplay failed:', error)
        })
      })

      videoRef.value.addEventListener('canplay', () => {
        console.log('Video can start playing')
      })
    }

    // Gallery intersection observer
    const observerOptions = {
      threshold: 0.2,
      rootMargin: '0px 0px -50px 0px',
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const index = galleryRefs.value.findIndex(ref => ref === entry.target)
          if (index !== -1) {
            galleryAnimated[index] = true
            observer.unobserve(entry.target)
          }
        }
      })
    }, observerOptions)

    // Observe gallery images
    setTimeout(() => {
      galleryRefs.value.forEach((ref) => {
        if (ref) {
          observer.observe(ref)
        }
      })
    }, 100)
  })
</script>

<style scoped>
.hero-section {
  position: relative;
  height: 600px;
  overflow: hidden;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

.hero-overlay {
  background: rgba(0, 0, 0, 0.4);
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 10;
}

.hero-title {
  font-size: 3rem;
  font-weight: 300;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.2rem;
  font-weight: 300;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 300;
  color: #666;
  text-align: center;
}

.section-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #666;
  text-align: center;
}

.services-section {
  background-color: #f8f6f3;
}

.service-card {
  background: transparent;
  text-align: center;
  padding: 2rem 1rem;
}

.service-title {
  font-size: 1.5rem;
  font-weight: 400;
  color: #666;
  text-align: center;
  margin-bottom: 1rem;
}

.service-text {
  font-size: 1rem;
  line-height: 1.6;
  color: #666;
  text-align: center;
}

.services-list {
  padding-left: 0;
}

.service-item {
  text-align: left;
}

.service-item-title {
  font-size: 1.5rem;
  font-weight: 500;
  color: #333;
  line-height: 1.3;
}

.service-item-text {
  font-size: 1rem;
  line-height: 1.6;
  color: #666;
}

.testimonial-card {
  background-color: #f8f6f3;
  border-radius: 16px;
}

.testimonial-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #666;
  font-style: italic;
}

.testimonial-author {
  font-weight: 500;
  color: #333;
}

.gallery-section {
  background-color: #f8f6f3;
}

.gallery-image-container {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.gallery-image-container.animate-fade-up {
  opacity: 1;
  transform: translateY(0);
}

.gallery-image {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.gallery-image:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.footer-section {
  background-color: #333;
  color: white;
}

.footer-title {
  font-size: 1.25rem;
  font-weight: 500;
  color: white;
}

.footer-text {
  font-size: 0.9rem;
  line-height: 1.6;
  color: #ccc;
}

@media (max-width: 960px) {
  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 2rem;
  }
}
</style>
